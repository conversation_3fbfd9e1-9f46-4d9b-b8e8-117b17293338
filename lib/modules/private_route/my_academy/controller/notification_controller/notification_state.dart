import 'package:equatable/equatable.dart';
import '../../model/notification_model.dart';

class NotificationState extends Equatable {
  @override
  List<Object?> get props => [];
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationSuccess extends NotificationState {
  final NotificationModel notificationModel;

  NotificationSuccess({required this.notificationModel});

  @override
  List<Object> get props => [identityHashCode(this)];
}

class NotificationFailure extends NotificationState {
  final String msg;

  NotificationFailure({required this.msg});

  @override
  List<Object> get props => [msg];
}

class DeleteNotificationLoading extends NotificationState {}

class DeleteNotificationSuccess extends NotificationState {
  final String msg;

  DeleteNotificationSuccess({required this.msg});

  @override
  List<Object> get props => [msg];
}

class DeleteNotificationFailure extends NotificationState {
  final String msg;

  DeleteNotificationFailure({required this.msg});

  @override
  List<Object> get props => [msg];
}
