import 'package:equatable/equatable.dart';

class NotificationEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class GetNotifications extends NotificationEvent {
  final int page;
  final int limit;

  GetNotifications({required this.page, required this.limit});

  @override
  List<Object?> get props => [page, limit];
}

class DeleteNotification extends NotificationEvent {
  final int notificationId;

  DeleteNotification({required this.notificationId});

  @override
  List<Object?> get props => [notificationId];
}
