import 'package:flutter/material.dart';

import '../../../../../global/constants/app_constant.dart';
import '../../../../../global/constants/size.dart';
import '../../../../../utils/constants/app_asset.dart';
import '../../../../../utils/constants/color_constant.dart';
import 'bottom_bar_item.dart';

class BottomBarHelper extends StatelessWidget {
  final int index;
  final Function(int) onItemTapped;
  final bool  isForCoach;

  const BottomBarHelper({
    super.key,
    required this.index,
    required this.isForCoach,
    required this.onItemTapped,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        height: MySize.getScaledSizeHeight(66),
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(5), topLeft: Radius.circular(5)),
            gradient: LinearGradient(colors: [
              ColorConstant.primaryColor,
              ColorConstant.secondaryColor,
            ])),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 18.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              BottomBarItem(
                image: AppAsset.basketBottom,
                isSelected: ValueNotifier(index == 0),
                onTap: () => onItemTapped(0),
                selectedImage: AppAsset.selectedBasketBottom,
              ),
              if (!isForCoach)
                Padding(
                  padding: const EdgeInsets.only(top: 3),
                  child: BottomBarItem(
                    image: AppAsset.selectedWallet,
                    isSelected: ValueNotifier(index == 1),
                    onTap: () {
                      onItemTapped(1);
                      AppConstant.isHomeScreen = false;
                    },
                    selectedImage: AppAsset.wallet,
                  ),
                ),
              BottomBarItem(
                image: AppAsset.heart,
                isSelected: ValueNotifier(index == 2),
                onTap: () {
                  onItemTapped(2);
                  AppConstant.isHomeScreen = false;
                },
                selectedImage: AppAsset.selectedHeart,
              ),
              BottomBarItem(
                image: AppAsset.notification,
                isSelected: ValueNotifier(index == 3),
                onTap: () {
                  onItemTapped(3);
                  AppConstant.isHomeScreen = false;
                },
                selectedImage: AppAsset.selectedNotification,
              ),
              ValueListenableBuilder(
                valueListenable: AppConstant.academyLogo,
                builder: (context, value, child) => BottomBarItem(
                  isForLogo: true,
                  image: AppConstant.academyLogo.value,
                  isSelected: ValueNotifier(index == 4),
                  onTap: () {
                    onItemTapped(4);
                    AppConstant.isHomeScreen = false;
                  },
                  selectedImage: AppAsset.feeSvg,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
