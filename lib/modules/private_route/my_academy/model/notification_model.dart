import 'package:khelnet/services/util_methods.dart';

class NotificationModel {
  String status;
  NotificationData data;

  NotificationModel({
    required this.status,
    required this.data,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      NotificationModel(
        status: UtilMethods().stringValueParser(json["status"]),
        data: UtilMethods()
            .methodValueParser(json["data"], NotificationData.fromJson)!,
      );
}

class NotificationData {
  int count;
  List<NotificationItem> rows;

  NotificationData({
    required this.count,
    required this.rows,
  });

  factory NotificationData.fromJson(Map<String, dynamic> json) =>
      NotificationData(
        count: UtilMethods().intValueParser(json["count"]),
        rows: UtilMethods()
            .listValueParser(json["rows"], NotificationItem.fromJson),
      );
}

class NotificationItem {
  int id;
  String title;
  String body;
  String clickAction;
  DateTime createdAt;
  DateTime updatedAt;
  int academyId;
  int? coachId;

  NotificationItem({
    required this.id,
    required this.title,
    required this.body,
    required this.clickAction,
    required this.createdAt,
    required this.updatedAt,
    required this.academyId,
    this.coachId,
  });

  factory NotificationItem.fromJson(Map<String, dynamic> json) =>
      NotificationItem(
        id: UtilMethods().intValueParser(json["id"]),
        title: UtilMethods().stringValueParser(json["title"]),
        body: UtilMethods().stringValueParser(json["body"]),
        clickAction: UtilMethods().stringValueParser(json["click_action"]),
        createdAt: UtilMethods().dateValueParser(json["createdAt"]),
        updatedAt: UtilMethods().dateValueParser(json["updatedAt"]),
        academyId: UtilMethods().intValueParser(json["academyId"]),
        coachId: UtilMethods().intValueParser(json["coachId"]) == 0 
            ? null 
            : UtilMethods().intValueParser(json["coachId"]),
      );
}
