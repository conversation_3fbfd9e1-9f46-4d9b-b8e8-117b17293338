import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:geocoding/geocoding.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:gap/gap.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:khelnet/common_widgets/custom_svg_picture.dart';
import 'package:khelnet/common_widgets/single_selection_bottom_sheet.dart';
import 'package:khelnet/common_widgets/toast/toast_utils.dart';
import 'package:khelnet/global/constants/app_constant.dart';
import 'package:khelnet/global/constants/gradient_text.dart';
import 'package:khelnet/global/constants/size.dart';
import 'package:khelnet/utils/constants/app_asset.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/constants/config.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:khelnet/utils/manager/storage_manager.dart';
import 'package:khelnet/utils/theme/typography.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

import '../../common_widgets/dialog/loader/custom_dialog.dart';
import '../../modules/private_route/add_coach/model/get_center_model.dart';
import '../../modules/private_route/attendance/model/get_all_student_report_model.dart';
import '../../modules/private_route/attendance/model/get_student_report_model.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../modules/private_route/fees/model/invoice_model.dart';
import '../../services/notification_services.dart';
import '../../utils/constants/routes_constant.dart';
import '../../utils/manager/navigation_manager.dart';

class GlobalMethods {
  static final GlobalMethods instance = GlobalMethods._();

  factory GlobalMethods() => instance;

  GlobalMethods._();

  Future<List<String>> getSuggestion(String input) async {
    List<String> placesList = [];
    placesList.clear();
    String apikey = 'AIzaSyDXn9_mVzBL1PO7JmVkB_ClNw-OM3357hw';
    String baseUrl =
        "https://maps.googleapis.com/maps/api/place/autocomplete/json";
    String request =
        '$baseUrl?input=$input&key=$apikey&sessiontoken=1122334455&components=country:in';

    var response = await http.get(Uri.parse(request));
    var data = response.body.toString();

    if (response.statusCode == 200) {
      if (jsonDecode(data)['predictions'].length == 0) {
        placesList.clear();
      } else {
        jsonDecode(data)['predictions'].forEach((element) {
          placesList.add(element["description"]);
        });
      }

      return placesList;
    } else {
      throw Exception('Failed to load data!');
    }
  }

  static generatePDF(BuildContext context,
      {required String studentName,
      required String image,
      required String startDate,
      required String endDate,
      required int absentCount,
      required Function() onComplete,
      bool isBackOn = false,
      required int presentCount,
      required List<AttendanceRecord> attendanceRecords,
      required String batchName}) async {
    ScreenshotController screenshotController = ScreenshotController();
    final int totalCount = absentCount + presentCount;
    final double chartAbsent = (absentCount / totalCount) * 100;
    final double chartPresent =
        totalCount == 0 ? 0 : (presentCount / totalCount) * 100;
    final widgetToCapture = MediaQuery(
        data: const MediaQueryData(),
        child: Screenshot(
          controller: screenshotController,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 18.0),
            child: Stack(
              children: [
                PieChart(
                  PieChartData(
                    sectionsSpace: 0,
                    startDegreeOffset: 90,
                    sections: [
                      PieChartSectionData(
                        color: const Color(0XFFFF5757),
                        value: absentCount.toDouble(),
                        badgeWidget: TypoGraphy.text(
                            "${chartAbsent.toStringAsFixed(1)}%",
                            fontSize: MySize.getScaledSizeHeight(24),
                            fontWeight: FontWeight.normal),
                        titleStyle: TextStyle(
                            color: ColorConstant.transparent,
                            fontFamily:
                                AppConstant.familyConstant.montserratSemiBold,
                            fontSize: MySize.getScaledSizeHeight(25)),
                        titlePositionPercentageOffset: -0.5,
                        radius: 80,
                      ),
                      PieChartSectionData(
                        color: const Color(0XFF7ED957),
                        value: presentCount.toDouble(),
                        badgeWidget: TypoGraphy.text(
                            "${chartPresent.toStringAsFixed(1)}%",
                            fontSize: MySize.getScaledSizeHeight(24),
                            fontWeight: FontWeight.normal),
                        // title: '${chartAbsent.toStringAsFixed(1)}%',
                        titleStyle: TextStyle(
                            color: ColorConstant.transparent,
                            fontFamily:
                                AppConstant.familyConstant.montserratSemiBold,
                            fontSize: MySize.getScaledSizeHeight(25)),
                        titlePositionPercentageOffset: -0.5,
                        radius: 80,
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: MySize.getScaledSizeHeight(410),
                  left: MySize.getScaledSizeWidth(165),
                  child: TypoGraphy.text('${chartPresent.toStringAsFixed(1)}%',
                      level: 6, color: const Color(0XFF7ED957)),
                )
              ],
            ),
          ),
        ));
    Uint8List? chartImage =
        await screenshotController.captureFromWidget(widgetToCapture);

    final img = await rootBundle.load(AppAsset.applicationLogo);
    final imageBytes = img.buffer.asUint8List();
    final netImage1 = await networkImage(image);
    pw.Image bottomImage = pw.Image(pw.MemoryImage(imageBytes));
    pw.Image resultImage = pw.Image(pw.MemoryImage(chartImage),
        height: MySize.getScaledSizeHeight(500),
        width: MySize.getScaledSizeWidth(500));
    final fontRegular = pw.Font.ttf(
        await rootBundle.load("assets/font/Montserrat-Regular.ttf"));
    final fontBold =
        pw.Font.ttf(await rootBundle.load("assets/font/Montserrat-Bold.ttf"));
    final fontSemiBold = pw.Font.ttf(
        await rootBundle.load("assets/font/Montserrat-SemiBold.ttf"));
    int totalStudent = absentCount + presentCount;
    double presentPercentage = 0;
    if (totalStudent == 0) {
      presentPercentage = 0;
    } else {
      presentPercentage = ((presentCount / totalStudent) * 100);
    }
    final pdf = pw.Document();
    final pageTheme = pw.PageTheme(
      pageFormat: PdfPageFormat.a4,
      buildBackground: (context) => pw.Container(),
      buildForeground: (context) => pw.Container(
          alignment: pw.Alignment.bottomCenter,
          margin: const pw.EdgeInsets.only(bottom: 10),
          child: pw.Row(children: [
            pw.Container(
                width: MySize.getScaledSizeWidth(20),
                height: MySize.getScaledSizeHeight(20),
                decoration: pw.BoxDecoration(
                    shape: pw.BoxShape.circle,
                    color: PdfColors.white,
                    border: pw.Border.all(
                      color: PdfColors.grey,
                    )),
                child: pw.Center(
                  child: pw.Text('${context.pageNumber}',
                      style: pw.TextStyle(
                          fontSize: MySize.getScaledSizeHeight(10))),
                )),
            pw.Expanded(child: pw.SizedBox.shrink()),
            pw.Text("Powered By- ",
                style: pw.TextStyle(
                    fontSize: MySize.getScaledSizeHeight(10),
                    font: fontRegular,
                    color: PdfColors.grey)),
            pw.Container(
              alignment: pw.Alignment.center,
              height: MySize.getScaledSizeHeight(50),
              width: MySize.getScaledSizeWidth(50),
              child: bottomImage,
            )
          ])),
    );
    pdf.addPage(
      pw.Page(
        pageTheme: pageTheme,
        build: (pw.Context context) => pw.Center(
          child: pw.Column(children: [
            pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Container(
                    height: MySize.getScaledSizeHeight(50),
                    width: MySize.getScaledSizeWidth(300),
                    child: pw.Center(
                      child: pw.Text("ATTENDANCE REPORT",
                          style: pw.TextStyle(
                              fontSize: MySize.getScaledSizeHeight(20),
                              font: fontBold,
                              color: const PdfColor.fromInt(0XFFFFFFFF))),
                    ),
                    color: const PdfColor.fromInt(0XFF38B6FF),
                  ),
                  pw.Padding(
                      padding: const pw.EdgeInsets.only(top: 10),
                      child: pw.Image(
                        netImage1,
                        height: MySize.getScaledSizeHeight(100),
                        width: MySize.getScaledSizeWidth(100),
                      ))
                ]),
            pw.SizedBox(height: MySize.getScaledSizeHeight(20)),
            pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.RichText(
                      text: pw.TextSpan(children: [
                    pw.TextSpan(
                        text: "Name - ",
                        style: pw.TextStyle(
                            font: fontRegular,
                            color: const PdfColor.fromInt(0xFF262626),
                            fontSize: MySize.getScaledSizeHeight(16))),
                    pw.TextSpan(
                        text: studentName,
                        style: pw.TextStyle(
                            font: fontBold,
                            color: const PdfColor.fromInt(0xFF262626),
                            fontSize: MySize.getScaledSizeHeight(14)))
                  ])),
                  pw.RichText(
                      text: pw.TextSpan(children: [
                    pw.TextSpan(
                        text: "Date Range - ",
                        style: pw.TextStyle(
                            font: fontRegular,
                            color: const PdfColor.fromInt(0xFF262626),
                            fontSize: MySize.getScaledSizeHeight(16))),
                    pw.TextSpan(
                        text: "$startDate - $endDate",
                        style: pw.TextStyle(
                            font: fontBold,
                            color: const PdfColor.fromInt(0xFF262626),
                            fontSize: MySize.getScaledSizeHeight(14)))
                  ])),
                ]),
            pw.SizedBox(height: MySize.getScaledSizeHeight(10)),
            pw.Align(
                alignment: pw.Alignment.centerLeft,
                child: pw.RichText(
                    text: pw.TextSpan(children: [
                  pw.TextSpan(
                      text: "Batch - ",
                      style: pw.TextStyle(
                          font: fontRegular,
                          color: const PdfColor.fromInt(0xFF262626),
                          fontSize: MySize.getScaledSizeHeight(16))),
                  pw.TextSpan(
                      text: batchName,
                      style: pw.TextStyle(
                          font: fontBold,
                          color: const PdfColor.fromInt(0xFF262626),
                          fontSize: MySize.getScaledSizeHeight(14)))
                ]))),
            pw.SizedBox(height: MySize.getScaledSizeHeight(15)),
            pw.Divider(
                color: const PdfColor.fromInt(0xFF262626),
                indent: 10,
                endIndent: 10),
            pw.SizedBox(height: MySize.getScaledSizeHeight(15)),
            pw.RichText(
                text: pw.TextSpan(children: [
              pw.TextSpan(
                  text: "$presentCount/${absentCount + presentCount}",
                  style: pw.TextStyle(
                      font: fontSemiBold,
                      color: const PdfColor.fromInt(0xFF262626),
                      fontSize: MySize.getScaledSizeHeight(25))),
              pw.TextSpan(
                  text: "   Session Attended",
                  style: pw.TextStyle(
                      font: fontSemiBold,
                      color: const PdfColor.fromInt(0xFF262626),
                      fontSize: MySize.getScaledSizeHeight(14)))
            ])),
            pw.Container(
              alignment: pw.Alignment.center,
              height: MySize.getScaledSizeHeight(400),
              width: MySize.getScaledSizeWidth(400),
              child: resultImage,
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 10),
              child: pw.Align(
                alignment: pw.Alignment.centerLeft,
                child: pw.RichText(
                    text: pw.TextSpan(children: [
                  pw.TextSpan(
                      text: "Total Session Scheduled- ",
                      style: pw.TextStyle(
                          font: fontSemiBold,
                          color: const PdfColor.fromInt(0xFF262626),
                          fontSize: MySize.getScaledSizeHeight(20))),
                  pw.TextSpan(
                      text: "${absentCount + presentCount}",
                      style: pw.TextStyle(
                          font: fontSemiBold,
                          color: const PdfColor.fromInt(0xFFFF5757),
                          fontSize: MySize.getScaledSizeHeight(20)))
                ])),
              ),
            ),
            pw.Padding(
                padding: const pw.EdgeInsets.only(bottom: 10),
                child: pw.Align(
                    alignment: pw.Alignment.centerLeft,
                    child: pw.RichText(
                        text: pw.TextSpan(children: [
                      pw.TextSpan(
                          text: "Total Session Attended- ",
                          style: pw.TextStyle(
                              font: fontSemiBold,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(20))),
                      pw.TextSpan(
                          text: "$presentCount",
                          style: pw.TextStyle(
                              font: fontBold,
                              color: const PdfColor.fromInt(0xFF7ED957),
                              fontSize: MySize.getScaledSizeHeight(20)))
                    ]))))
          ]),
        ),
      ),
    );
    List<pw.Widget> widget = [];
    widget.add(
      pw.Align(
        alignment: pw.Alignment.centerLeft,
        child: pw.Text(
          "DETAILED SUMMARY",
          style: pw.TextStyle(
            font: fontBold,
            fontWeight: pw.FontWeight.bold,
            fontSize: MySize.getScaledSizeHeight(20),
          ),
        ),
      ),
    );
    widget.add(
      pw.SizedBox(height: 10),
    );
    widget.add(
      pw.Table(
        border: pw.TableBorder.all(),
        columnWidths: {
          0: pw.FixedColumnWidth(MySize.getScaledSizeWidth(50)),
          1: const pw.FlexColumnWidth(),
          2: const pw.FlexColumnWidth(),
          3: const pw.FlexColumnWidth(),
        },
        children: [
          pw.TableRow(
            decoration: const pw.BoxDecoration(
              color: PdfColor.fromInt(0xFFECF0F2),
            ),
            children: [
              pw.Padding(
                padding: const pw.EdgeInsets.all(8.0),
                child: pw.Text(
                  "S No.",
                  style: pw.TextStyle(
                    font: fontRegular,
                    color: const PdfColor.fromInt(0xFF737373),
                    fontSize: MySize.getScaledSizeHeight(14),
                  ),
                ),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8.0),
                child: pw.Text(
                  "Session Date",
                  style: pw.TextStyle(
                    font: fontRegular,
                    color: const PdfColor.fromInt(0xFF737373),
                    fontSize: MySize.getScaledSizeHeight(14),
                  ),
                ),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8.0),
                child: pw.Text(
                  "Day",
                  style: pw.TextStyle(
                    font: fontRegular,
                    color: const PdfColor.fromInt(0xFF737373),
                    fontSize: MySize.getScaledSizeHeight(14),
                  ),
                ),
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.all(8.0),
                child: pw.Text(
                  "Attendance",
                  style: pw.TextStyle(
                    font: fontRegular,
                    color: const PdfColor.fromInt(0xFF737373),
                    fontSize: MySize.getScaledSizeHeight(14),
                  ),
                ),
              ),
            ],
          ),
          ...attendanceRecords.asMap().entries.map((entry) {
            int index = entry.key;
            return pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    (index + 1).toString(),
                    style: pw.TextStyle(
                      font: fontSemiBold,
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    DateFormat('dd/MM/yyyy')
                        .format(DateTime.parse(entry.value.date)),
                    style: pw.TextStyle(
                      font: fontSemiBold,
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    entry.value.day,
                    style: pw.TextStyle(
                      font: fontSemiBold,
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    entry.value.isPresent ? "Present" : "Absent",
                    style: pw.TextStyle(
                      font: fontBold,
                      fontSize: MySize.getScaledSizeHeight(14),
                      color: PdfColor.fromInt(
                        entry.value.isPresent ? 0xFF2DE150 : 0xFFFF3131,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
    widget.add(
      pw.Divider(indent: 400, color: const PdfColor.fromInt(0xFF262626)),
    );
    widget.add(
      pw.Align(
        alignment: pw.Alignment.centerRight,
        child: pw.RichText(
          text: pw.TextSpan(
            children: [
              pw.TextSpan(
                text: "$presentCount/${absentCount + presentCount}",
                style: pw.TextStyle(
                  font: fontBold,
                  color: const PdfColor.fromInt(0xFF262626),
                  fontSize: MySize.getScaledSizeHeight(20),
                ),
              ),
              pw.TextSpan(
                text: " (${presentPercentage.toStringAsFixed(2)} %)",
                style: pw.TextStyle(
                  font: fontBold,
                  fontWeight: pw.FontWeight.normal,
                  color: const PdfColor.fromInt(0xFF262626),
                  fontSize: MySize.getScaledSizeHeight(20),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    // widget.add(pw.Center(
    //   child: pw.Column(
    //     crossAxisAlignment: pw.CrossAxisAlignment.start,
    //     children: [
    //
    //
    //     ],
    //   ),
    // ));
    pdf.addPage(
      pw.MultiPage(
        pageTheme: pageTheme,
        // margin: const pw.EdgeInsets.symmetric(horizontal: 20),
        build: (context) => widget,
      ),
    );

    try {
      // DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      // AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;

      await requestPermissions(
        context,
        permissions: [
          Platform.isIOS ? Permission.photos : Permission.mediaLibrary
        ],
        onPermissionsGranted: () async {
          // Directory? tempDir = Platform.isIOS
          //     ? await getApplicationSupportDirectory()
          //     : await getApplicationSupportDirectory();
          // // final tempDir = Directory('/storage/emulated/0/Download/khelnet');
          // if (!(await tempDir.exists())) {
          //   await tempDir.create(recursive: true);
          // }
          final path = await getDownloadFolderPath();
          final file =
              File('$path/$studentName Attendance $startDate to $endDate.pdf');
          try {
            await file.writeAsBytes(await pdf.save()).then((value) async {
              if (file.path.isNotEmpty && context.mounted && file.path != "") {
                CustomDialog.hideLoader(context);
                onComplete();
                context.pushNamed(RouteConstant.pdfViewScreen,
                    args: {"pdfPath": file.path}).then(
                  (value) {
                    if (isBackOn) {
                      CustomDialog.hideLoader(context);
                    }
                  },
                );
              }
            });
          } catch (e) {
            log(e.toString());
            CustomDialog.hideLoader(context);
            ToastUtils.showFailed(message: "Something went wrong");
          }
          log('PDF saved at ${file.path}');
        },
      );
    } catch (e) {
      log("Error generating PDF: $e");
    }
  }

  static generateReport(
    BuildContext context, {
    required String startDate,
    required String image,
    required String endDate,
    required List<String> centers,
    required List<String> batches,
    required void Function() onDone,
    required bool isForReport,
    required List<AllStudentReportData> allStudentReportData,
  }) async {
    double totalPercentage = allStudentReportData.fold(
      0.0,
      (sum, report) => sum + report.percentage,
    );
    final netImage1 = await networkImage(image);
    final fontRegular = pw.Font.ttf(
        await rootBundle.load("assets/font/Montserrat-Regular.ttf"));
    final fontBold =
        pw.Font.ttf(await rootBundle.load("assets/font/Montserrat-Bold.ttf"));
    final fontSemiBold = pw.Font.ttf(
        await rootBundle.load("assets/font/Montserrat-SemiBold.ttf"));
    final pdf = pw.Document();
    List<AllStudentReportData> firstPageList = [];
    List<AllStudentReportData> secondPageList = [];

    if (allStudentReportData.length > 10) {
      firstPageList = allStudentReportData.sublist(0, 10);
      secondPageList = allStudentReportData.sublist(10);
    } else {
      firstPageList = allStudentReportData;
    }
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) => pw.Center(
            child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Container(
                      height: MySize.getScaledSizeHeight(50),
                      width: MySize.getScaledSizeWidth(300),
                      child: pw.Center(
                        child: pw.Text("ATTENDANCE REPORT",
                            style: pw.TextStyle(
                                fontSize: MySize.getScaledSizeHeight(20),
                                font: fontBold,
                                color: const PdfColor.fromInt(0XFFFFFFFF))),
                      ),
                      color: const PdfColor.fromInt(0XFF38B6FF),
                    ),
                    pw.Padding(
                        padding: const pw.EdgeInsets.only(top: 10),
                        child: pw.Image(
                          netImage1,
                          height: MySize.getScaledSizeHeight(100),
                          width: MySize.getScaledSizeWidth(100),
                        ))
                  ]),
              pw.SizedBox(height: MySize.getScaledSizeHeight(20)),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.RichText(
                        text: pw.TextSpan(children: [
                      pw.TextSpan(
                          text: "Centers(s) - ",
                          style: pw.TextStyle(
                              font: fontRegular,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(16))),
                      pw.TextSpan(
                          text: centers.length == allStudentReportData.length
                              ? "All"
                              : centers.length.toString(),
                          style: pw.TextStyle(
                              font: fontBold,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(14)))
                    ])),
                    pw.RichText(
                        text: pw.TextSpan(children: [
                      pw.TextSpan(
                          text: "Report Type - ",
                          style: pw.TextStyle(
                              font: fontRegular,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(16))),
                      pw.TextSpan(
                          text: "Combined",
                          style: pw.TextStyle(
                              font: fontBold,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(14)))
                    ])),
                  ]),
              pw.SizedBox(height: MySize.getScaledSizeHeight(20)),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.RichText(
                        text: pw.TextSpan(children: [
                      pw.TextSpan(
                          text: "Batches - ",
                          style: pw.TextStyle(
                              font: fontRegular,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(16))),
                      pw.TextSpan(
                          text: batches.length == allStudentReportData.length
                              ? "All"
                              : batches.length.toString(),
                          style: pw.TextStyle(
                              font: fontBold,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(14)))
                    ])),
                    pw.RichText(
                        text: pw.TextSpan(children: [
                      pw.TextSpan(
                          text: "Date Range - ",
                          style: pw.TextStyle(
                              font: fontRegular,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(16))),
                      pw.TextSpan(
                          text: "$startDate - $endDate",
                          style: pw.TextStyle(
                              font: fontBold,
                              color: const PdfColor.fromInt(0xFF262626),
                              fontSize: MySize.getScaledSizeHeight(14)))
                    ])),
                  ]),
              pw.SizedBox(height: MySize.getScaledSizeHeight(20)),
              pw.Divider(
                  color: const PdfColor.fromInt(0xFF262626),
                  indent: 10,
                  endIndent: 10),
              pw.SizedBox(height: MySize.getScaledSizeHeight(15)),
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.center, children: [
                pw.Text("Average Attendance %-    ",
                    style: pw.TextStyle(
                        font: fontRegular,
                        color: const PdfColor.fromInt(0xFF262626),
                        fontSize: MySize.getScaledSizeHeight(20))),
                pw.Text(
                    "${allStudentReportData.isNotEmpty ? (totalPercentage / allStudentReportData.length).toStringAsFixed(2) : 0} %  ",
                    style: pw.TextStyle(
                        font: fontBold,
                        color: const PdfColor.fromInt(0xFF00BF63),
                        fontSize: MySize.getScaledSizeHeight(20))),
                // pw.Text("↑",
                //     style: pw.TextStyle(
                //         font: fontBold,
                //         color: const PdfColor.fromInt(0xFF7ED957),
                //         fontSize: MySize.getScaledSizeHeight(40))),
                // pw.Text("  11% (Since Last Month)*",
                //     style: pw.TextStyle(
                //         font: fontBold,
                //         color: const PdfColor.fromInt(0xFF00BF63),
                //         fontSize: MySize.getScaledSizeHeight(12))),
              ]),
              pw.SizedBox(height: MySize.getScaledSizeHeight(20)),
              pw.Padding(
                padding: const pw.EdgeInsets.only(right: 10),
                child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.start,
                    children: [
                      pw.Expanded(
                        child: pw.Column(children: [
                          pw.Text("Player Info ",
                              style: pw.TextStyle(font: fontSemiBold)),
                          pw.SizedBox(height: MySize.getScaledSizeHeight(15)),
                          pw.Table(
                            columnWidths: {
                              0: pw.FixedColumnWidth(
                                  MySize.getScaledSizeWidth(30)),
                              1: pw.FixedColumnWidth(
                                  MySize.getScaledSizeWidth(90)),
                              2: pw.FixedColumnWidth(
                                  MySize.getScaledSizeWidth(90)),
                              3: pw.FixedColumnWidth(
                                  MySize.getScaledSizeWidth(90)),
                            },
                            border: pw.TableBorder.all(),
                            children: [
                              pw.TableRow(
                                decoration: const pw.BoxDecoration(
                                  color: PdfColor.fromInt(0xFFECF0F2),
                                ),
                                children: [
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.all(8.0),
                                    child: pw.Text(
                                      "S No.",
                                      maxLines: 1,
                                      style: pw.TextStyle(
                                        font: fontRegular,
                                        color:
                                            const PdfColor.fromInt(0xFF737373),
                                        fontSize:
                                            MySize.getScaledSizeHeight(14),
                                      ),
                                    ),
                                  ),
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.all(8.0),
                                    child: pw.Text(
                                      "Student Name",
                                      maxLines: 1,
                                      style: pw.TextStyle(
                                        font: fontRegular,
                                        color:
                                            const PdfColor.fromInt(0xFF737373),
                                        fontSize:
                                            MySize.getScaledSizeHeight(14),
                                      ),
                                    ),
                                  ),
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.all(8.0),
                                    child: pw.Text(
                                      "Center",
                                      maxLines: 1,
                                      style: pw.TextStyle(
                                        font: fontRegular,
                                        color:
                                            const PdfColor.fromInt(0xFF737373),
                                        fontSize:
                                            MySize.getScaledSizeHeight(14),
                                      ),
                                    ),
                                  ),
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.all(8.0),
                                    child: pw.Text(
                                      "Batch",
                                      maxLines: 1,
                                      style: pw.TextStyle(
                                        font: fontRegular,
                                        color:
                                            const PdfColor.fromInt(0xFF737373),
                                        fontSize:
                                            MySize.getScaledSizeHeight(12),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              ...firstPageList.asMap().entries.map((entry) {
                                int index = entry.key;
                                return pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding: const pw.EdgeInsets.all(8.0),
                                      child: pw.Text(
                                        (index + 1).toString(),
                                        style: pw.TextStyle(
                                          font: fontSemiBold,
                                          fontSize:
                                              MySize.getScaledSizeHeight(12),
                                        ),
                                      ),
                                    ),
                                    pw.Padding(
                                      padding: const pw.EdgeInsets.all(8.0),
                                      child: pw.Text(
                                        entry.value.studentName,
                                        maxLines: 1,
                                        style: pw.TextStyle(
                                          font: fontSemiBold,
                                          fontSize:
                                              MySize.getScaledSizeHeight(12),
                                        ),
                                      ),
                                    ),
                                    pw.Padding(
                                      padding: const pw.EdgeInsets.all(8.0),
                                      child: pw.Text(
                                        entry.value.center,
                                        maxLines: 1,
                                        style: pw.TextStyle(
                                          font: fontSemiBold,
                                          fontSize:
                                              MySize.getScaledSizeHeight(12),
                                        ),
                                      ),
                                    ),
                                    pw.Padding(
                                      padding: const pw.EdgeInsets.all(8.0),
                                      child: pw.Text(
                                        maxLines: 1,
                                        entry.value.batch,
                                        style: pw.TextStyle(
                                          font: fontSemiBold,
                                          fontSize:
                                              MySize.getScaledSizeHeight(12),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }),
                            ],
                          ),
                        ]),
                      ),
                      pw.SizedBox(
                        width: MySize.getScaledSizeWidth(7),
                      ),
                      pw.Column(children: [
                        pw.Text("Attendance*",
                            style: pw.TextStyle(font: fontSemiBold)),
                        pw.SizedBox(height: MySize.getScaledSizeHeight(15)),
                        pw.Table(
                          columnWidths: {
                            0: pw.FixedColumnWidth(
                                MySize.getScaledSizeWidth(40)),
                            1: pw.FixedColumnWidth(
                                MySize.getScaledSizeWidth(50)),
                            2: pw.FixedColumnWidth(
                                MySize.getScaledSizeWidth(50)),
                          },
                          border: pw.TableBorder.all(),
                          children: [
                            pw.TableRow(
                              decoration: const pw.BoxDecoration(
                                color: PdfColor.fromInt(0xFFECF0F2),
                              ),
                              children: [
                                pw.Padding(
                                  padding: const pw.EdgeInsets.all(8.0),
                                  child: pw.Text(
                                    "Present",
                                    maxLines: 1,
                                    style: pw.TextStyle(
                                      font: fontRegular,
                                      color: const PdfColor.fromInt(0xFF737373),
                                      fontSize: MySize.getScaledSizeHeight(14),
                                    ),
                                  ),
                                ),
                                pw.Padding(
                                  padding: const pw.EdgeInsets.all(8.0),
                                  child: pw.Text(
                                    "Absent",
                                    maxLines: 1,
                                    style: pw.TextStyle(
                                      font: fontRegular,
                                      color: const PdfColor.fromInt(0xFF737373),
                                      fontSize: MySize.getScaledSizeHeight(14),
                                    ),
                                  ),
                                ),
                                pw.Padding(
                                  padding: const pw.EdgeInsets.all(8.0),
                                  child: pw.Text(
                                    "%",
                                    maxLines: 1,
                                    style: pw.TextStyle(
                                      font: fontRegular,
                                      color: const PdfColor.fromInt(0xFF737373),
                                      fontSize: MySize.getScaledSizeHeight(14),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            ...firstPageList.asMap().entries.map((entry) {
                              return pw.TableRow(
                                children: [
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.all(8.0),
                                    child: pw.Text(
                                      entry.value.present.toString(),
                                      maxLines: 1,
                                      style: pw.TextStyle(
                                        font: fontSemiBold,
                                        fontSize:
                                            MySize.getScaledSizeHeight(12),
                                        color: const PdfColor.fromInt(
                                          0xFF2DE150,
                                        ),
                                      ),
                                    ),
                                  ),
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.all(8.0),
                                    child: pw.Text(
                                      entry.value.absent.toString(),
                                      maxLines: 1,
                                      style: pw.TextStyle(
                                        font: fontSemiBold,
                                        fontSize:
                                            MySize.getScaledSizeHeight(12),
                                        color: const PdfColor.fromInt(
                                          0xFFFF3131,
                                        ),
                                      ),
                                    ),
                                  ),
                                  pw.Padding(
                                    padding: const pw.EdgeInsets.all(8.0),
                                    child: pw.Text(
                                      "${entry.value.percentage.toString()}%",
                                      maxLines: 1,
                                      style: pw.TextStyle(
                                        font: fontSemiBold,
                                        fontSize:
                                            MySize.getScaledSizeHeight(12),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }),
                          ],
                        ),
                      ])
                    ]),
              )
            ])),
      ),
    );
    List<pw.Widget> widget = [];
    if (allStudentReportData.length > 10) {
      widget.add(pw.Padding(
        padding: const pw.EdgeInsets.only(right: 10),
        // child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.center ,children: [
        //   pw.Table(
        //     columnWidths: {
        //       0: pw.FixedColumnWidth(MySize.getScaledSizeWidth(40)),
        //       1: pw.FixedColumnWidth(MySize.getScaledSizeWidth(90)),
        //       2: pw.FixedColumnWidth(MySize.getScaledSizeWidth(90)),
        //       3: pw.FixedColumnWidth(MySize.getScaledSizeWidth(90)),
        //     },
        //     border: pw.TableBorder.all(),
        //     children: [
        //       pw.TableRow(
        //         decoration: const pw.BoxDecoration(
        //           color: PdfColor.fromInt(0xFFECF0F2),
        //         ),
        //         children: [
        //           pw.Padding(
        //             padding: const pw.EdgeInsets.all(8.0),
        //             child: pw.Text(
        //               "S No.",
        //               maxLines: 1,
        //               style: pw.TextStyle(
        //                 font: fontRegular,
        //                 color: const PdfColor.fromInt(0xFF737373),
        //                 fontSize: MySize.getScaledSizeHeight(14),
        //               ),
        //             ),
        //           ),
        //           pw.Padding(
        //             padding: const pw.EdgeInsets.all(8.0),
        //             child: pw.Text(
        //               "Student Name",
        //               maxLines: 1,
        //               style: pw.TextStyle(
        //                 font: fontRegular,
        //                 color: const PdfColor.fromInt(0xFF737373),
        //                 fontSize: MySize.getScaledSizeHeight(14),
        //               ),
        //             ),
        //           ),
        //           pw.Padding(
        //             padding: const pw.EdgeInsets.all(8.0),
        //             child: pw.Text(
        //               "Center",
        //               maxLines: 1,
        //               style: pw.TextStyle(
        //                 font: fontRegular,
        //                 color: const PdfColor.fromInt(0xFF737373),
        //                 fontSize: MySize.getScaledSizeHeight(14),
        //               ),
        //             ),
        //           ),
        //           pw.Padding(
        //             padding: const pw.EdgeInsets.all(8.0),
        //             child: pw.Text(
        //               "Batch",
        //               maxLines: 1,
        //               style: pw.TextStyle(
        //                 font: fontRegular,
        //                 color: const PdfColor.fromInt(0xFF737373),
        //                 fontSize: MySize.getScaledSizeHeight(12),
        //               ),
        //             ),
        //           ),
        //         ],
        //       ),
        //       ...firstPageList.asMap().entries.map((entry) {
        //         int index = entry.key;
        //         return pw.TableRow(
        //           children: [
        //             pw.Padding(
        //               padding: const pw.EdgeInsets.all(8.0),
        //               child: pw.Text(
        //                 (index + 1).toString(),
        //                 style: pw.TextStyle(
        //                   font: fontSemiBold,
        //                   fontSize: MySize.getScaledSizeHeight(12),
        //                 ),
        //               ),
        //             ),
        //             pw.Padding(
        //               padding: const pw.EdgeInsets.all(8.0),
        //               child: pw.Text(
        //                 entry.value.studentName,
        //                 maxLines: 1,
        //                 style: pw.TextStyle(
        //                   font: fontSemiBold,
        //                   fontSize: MySize.getScaledSizeHeight(12),
        //                 ),
        //               ),
        //             ),
        //             pw.Padding(
        //               padding: const pw.EdgeInsets.all(8.0),
        //               child: pw.Text(
        //                 entry.value.center,
        //                 maxLines: 1,
        //                 style: pw.TextStyle(
        //                   font: fontSemiBold,
        //                   fontSize: MySize.getScaledSizeHeight(12),
        //                 ),
        //               ),
        //             ),
        //             pw.Padding(
        //               padding: const pw.EdgeInsets.all(8.0),
        //               child: pw.Text(
        //                 maxLines: 1,
        //                 entry.value.batch,
        //                 style: pw.TextStyle(
        //                   font: fontSemiBold,
        //                   fontSize: MySize.getScaledSizeHeight(12),
        //                 ),
        //               ),
        //             ),
        //           ],
        //         );
        //       }),
        //     ],
        //   ),
        //   pw.SizedBox(
        //     width: MySize.getScaledSizeWidth(7),
        //   ),
        //   pw.Table(
        //     columnWidths: {
        //       0: pw.FixedColumnWidth(MySize.getScaledSizeWidth(40)),
        //       1: pw.FixedColumnWidth(MySize.getScaledSizeWidth(50)),
        //       2: pw.FixedColumnWidth(MySize.getScaledSizeWidth(50)),
        //     },
        //     border: pw.TableBorder.all(),
        //     children: [
        //       pw.TableRow(
        //         decoration: const pw.BoxDecoration(
        //           color: PdfColor.fromInt(0xFFECF0F2),
        //         ),
        //         children: [
        //           pw.Padding(
        //             padding: const pw.EdgeInsets.all(8.0),
        //             child: pw.Text(
        //               "Present",
        //               maxLines: 1,
        //               style: pw.TextStyle(
        //                 font: fontRegular,
        //                 color: const PdfColor.fromInt(0xFF737373),
        //                 fontSize: MySize.getScaledSizeHeight(14),
        //               ),
        //             ),
        //           ),
        //           pw.Padding(
        //             padding: const pw.EdgeInsets.all(8.0),
        //             child: pw.Text(
        //               "Absent",
        //               maxLines: 1,
        //               style: pw.TextStyle(
        //                 font: fontRegular,
        //                 color: const PdfColor.fromInt(0xFF737373),
        //                 fontSize: MySize.getScaledSizeHeight(14),
        //               ),
        //             ),
        //           ),
        //           pw.Padding(
        //             padding: const pw.EdgeInsets.all(8.0),
        //             child: pw.Text(
        //               "%",
        //               maxLines: 1,
        //               style: pw.TextStyle(
        //                 font: fontRegular,
        //                 color: const PdfColor.fromInt(0xFF737373),
        //                 fontSize: MySize.getScaledSizeHeight(14),
        //               ),
        //             ),
        //           ),
        //         ],
        //       ),
        //       ...secondPageList.asMap().entries.map((entry) {
        //         return pw.TableRow(
        //           children: [
        //             pw.Padding(
        //               padding: const pw.EdgeInsets.all(8.0),
        //               child: pw.Text(
        //                 entry.value.present.toString(),
        //                 maxLines: 1,
        //                 style: pw.TextStyle(
        //                   font: fontSemiBold,
        //                   fontSize: MySize.getScaledSizeHeight(12),
        //                   color: const PdfColor.fromInt(
        //                     0xFF2DE150,
        //                   ),
        //                 ),
        //               ),
        //             ),
        //             pw.Padding(
        //               padding: const pw.EdgeInsets.all(8.0),
        //               child: pw.Text(
        //                 entry.value.absent.toString(),
        //                 maxLines: 1,
        //                 style: pw.TextStyle(
        //                   font: fontSemiBold,
        //                   fontSize: MySize.getScaledSizeHeight(12),
        //                   color: const PdfColor.fromInt(
        //                     0xFFFF3131,
        //                   ),
        //                 ),
        //               ),
        //             ),
        //             pw.Padding(
        //               padding: const pw.EdgeInsets.all(8.0),
        //               child: pw.Text(
        //                 "${entry.value.percentage.toString()}%",
        //                 maxLines: 1,
        //                 style: pw.TextStyle(
        //                   font: fontSemiBold,
        //                   fontSize: MySize.getScaledSizeHeight(12),
        //                 ),
        //               ),
        //             ),
        //           ],
        //         );
        //       }),
        //     ],
        //   ),
        // ]),
        child: pw.Table(
          columnWidths: {
            0: pw.FixedColumnWidth(MySize.getScaledSizeWidth(40)),
            1: pw.FixedColumnWidth(MySize.getScaledSizeWidth(90)),
            2: pw.FixedColumnWidth(MySize.getScaledSizeWidth(90)),
            3: pw.FixedColumnWidth(MySize.getScaledSizeWidth(90)),
            4: pw.FixedColumnWidth(MySize.getScaledSizeWidth(40)),
            5: pw.FixedColumnWidth(MySize.getScaledSizeWidth(50)),
            6: pw.FixedColumnWidth(MySize.getScaledSizeWidth(50)),
          },
          border: pw.TableBorder.all(),
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(
                color: PdfColor.fromInt(0xFFECF0F2),
              ),
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    "S No.",
                    maxLines: 1,
                    style: pw.TextStyle(
                      font: fontRegular,
                      color: const PdfColor.fromInt(0xFF737373),
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    "Student Name",
                    maxLines: 1,
                    style: pw.TextStyle(
                      font: fontRegular,
                      color: const PdfColor.fromInt(0xFF737373),
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    "Center",
                    maxLines: 1,
                    style: pw.TextStyle(
                      font: fontRegular,
                      color: const PdfColor.fromInt(0xFF737373),
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    "Batch",
                    maxLines: 1,
                    style: pw.TextStyle(
                      font: fontRegular,
                      color: const PdfColor.fromInt(0xFF737373),
                      fontSize: MySize.getScaledSizeHeight(12),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    "Present",
                    maxLines: 1,
                    style: pw.TextStyle(
                      font: fontRegular,
                      color: const PdfColor.fromInt(0xFF737373),
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    "Absent",
                    maxLines: 1,
                    style: pw.TextStyle(
                      font: fontRegular,
                      color: const PdfColor.fromInt(0xFF737373),
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8.0),
                  child: pw.Text(
                    "%",
                    maxLines: 1,
                    style: pw.TextStyle(
                      font: fontRegular,
                      color: const PdfColor.fromInt(0xFF737373),
                      fontSize: MySize.getScaledSizeHeight(14),
                    ),
                  ),
                ),
              ],
            ),
            ...secondPageList.asMap().entries.map((entry) {
              int index = entry.key;
              return pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8.0),
                    child: pw.Text(
                      (index + 1 + 10).toString(),
                      style: pw.TextStyle(
                        font: fontSemiBold,
                        fontSize: MySize.getScaledSizeHeight(12),
                      ),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8.0),
                    child: pw.Text(
                      entry.value.studentName,
                      maxLines: 1,
                      style: pw.TextStyle(
                        font: fontSemiBold,
                        fontSize: MySize.getScaledSizeHeight(12),
                      ),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8.0),
                    child: pw.Text(
                      entry.value.center,
                      maxLines: 1,
                      style: pw.TextStyle(
                        font: fontSemiBold,
                        fontSize: MySize.getScaledSizeHeight(12),
                      ),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8.0),
                    child: pw.Text(
                      maxLines: 1,
                      entry.value.batch,
                      style: pw.TextStyle(
                        font: fontSemiBold,
                        fontSize: MySize.getScaledSizeHeight(12),
                      ),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8.0),
                    child: pw.Text(
                      entry.value.present.toString(),
                      maxLines: 1,
                      style: pw.TextStyle(
                        font: fontSemiBold,
                        fontSize: MySize.getScaledSizeHeight(12),
                        color: const PdfColor.fromInt(
                          0xFF2DE150,
                        ),
                      ),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8.0),
                    child: pw.Text(
                      entry.value.absent.toString(),
                      maxLines: 1,
                      style: pw.TextStyle(
                        font: fontSemiBold,
                        fontSize: MySize.getScaledSizeHeight(12),
                        color: const PdfColor.fromInt(
                          0xFFFF3131,
                        ),
                      ),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8.0),
                    child: pw.Text(
                      "${entry.value.percentage.toString()}%",
                      maxLines: 1,
                      style: pw.TextStyle(
                        font: fontSemiBold,
                        fontSize: MySize.getScaledSizeHeight(12),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ));
    }
    if (allStudentReportData.length > 10) {
      pdf.addPage(pw.MultiPage(
          build: (context) => widget, margin: const pw.EdgeInsets.all(40)));
    }
    try {
      // DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      // AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;

      await requestPermissions(
        context,
        permissions: [
          Platform.isIOS ? Permission.photos : Permission.mediaLibrary
        ],
        onPermissionsGranted: () async {
          final path = await getDownloadFolderPath();
          final file = File(
              '$path/Attendance Combined Report $startDate to $endDate.pdf');
          try {
            await file.writeAsBytes(await pdf.save()).then((value) async {
              if (file.path.isNotEmpty && context.mounted && file.path != "") {
                if (isForReport) {
                  CustomDialog.hideLoader(context);
                }
                context.pushNamed(RouteConstant.pdfViewScreen,
                    args: {"pdfPath": file.path}).then(
                  (value) {
                    context.pop();
                    onDone();
                  },
                );
              }
            });
          } catch (e) {
            CustomDialog.hideLoader(context);
            CustomDialog.hideLoader(context);
            ToastUtils.showFailed(message: "Something went wrong");
            log("Error generating PDF: $e");
          }
          log('PDF saved at ${file.path}');
        },
      );
    } catch (e) {
      CustomDialog.hideLoader(context);
      ToastUtils.showFailed(message: "Something went wrong");
      log("Error generating PDF: $e");
    }
  }

  static invoicePdf(
    BuildContext context, {
    required InvoiceModel userInvoiceData,
    required String date,
    bool isForCharge = false,
    bool isSharing = false,
    required bool hasGst,
  }) async {
    var items = userInvoiceData.invoiceData.items;

    if (isForCharge) {
      items = items.sublist(1);
    } else {
      userInvoiceData.invoiceData.items;
    }
    final pdf = pw.Document();
    final netImage1 =
        await networkImage(userInvoiceData.invoiceData.academy.logo);
    final netImage2 = userInvoiceData.invoiceData.signature != "-"
        ? await networkImage(userInvoiceData.invoiceData.signature)
        : null;
    final fontRegular = pw.Font.ttf(
        await rootBundle.load("assets/font/Montserrat-Regular.ttf"));
    final fontBold =
        pw.Font.ttf(await rootBundle.load("assets/font/Montserrat-Bold.ttf"));
    final fontSemiBold = pw.Font.ttf(
        await rootBundle.load("assets/font/Montserrat-SemiBold.ttf"));

    final img = await rootBundle.load(AppAsset.applicationLogo);
    final imageBytes = img.buffer.asUint8List();
    pw.Image bottomImage = pw.Image(pw.MemoryImage(imageBytes));
    final pageTheme = pw.PageTheme(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(15),
      buildBackground: (context) => pw.Container(),
      buildForeground: (context) => pw.Container(
          alignment: pw.Alignment.bottomCenter,
          margin: const pw.EdgeInsets.only(bottom: 10),
          child: pw.Row(children: [
            pw.Expanded(child: pw.SizedBox.shrink()),
            pw.Text("Powered By- ",
                style: pw.TextStyle(
                    fontSize: MySize.getScaledSizeHeight(10),
                    font: fontRegular,
                    color: PdfColors.grey)),
            pw.Container(
              alignment: pw.Alignment.center,
              height: MySize.getScaledSizeHeight(50),
              width: MySize.getScaledSizeWidth(50),
              child: bottomImage,
            )
          ])),
    );
    pdf.addPage(pw.Page(
        pageTheme: pageTheme,
        build: (pw.Context context) => pw.Center(
              child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Image(netImage1,
                              width: MySize.getScaledSizeWidth(120),
                              height: MySize.getScaledSizeHeight(120)),
                          pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.end,
                              children: [
                                pw.SizedBox(
                                    width: MySize.getScaledSizeWidth(400),
                                    child: pw.Text(
                                        maxLines: 3,
                                        userInvoiceData
                                            .invoiceData.academy.name,
                                        textAlign: pw.TextAlign.end,
                                        style: pw.TextStyle(
                                            color: PdfColors.black,
                                            fontWeight: pw.FontWeight.bold,
                                            fontSize:
                                                MySize.getScaledSizeHeight(
                                                    20)))),
                                pw.SizedBox(
                                  height: MySize.getScaledSizeHeight(10),
                                ),
                                pw.SizedBox(
                                  width: MySize.getScaledSizeWidth(400),
                                  child: pw.Text(
                                      textAlign: pw.TextAlign.end,
                                      maxLines: 3,
                                      userInvoiceData
                                          .invoiceData.academy.address,
                                      style: pw.TextStyle(
                                          fontSize:
                                              MySize.getScaledSizeHeight(15),
                                          color: PdfColors.black,
                                          font: fontRegular)),
                                )
                              ])
                        ]),
                    pw.SizedBox(
                      height: MySize.getScaledSizeHeight(15),
                    ),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Container(
                            height: MySize.getScaledSizeHeight(70),
                            width: MySize.getScaledSizeWidth(200),
                            child: pw.Center(
                              child: pw.Text("INVOICE",
                                  style: pw.TextStyle(
                                      fontSize: MySize.getScaledSizeHeight(35),
                                      font: fontBold,
                                      color:
                                          const PdfColor.fromInt(0XFFFFFFFF))),
                            ),
                            color: const PdfColor.fromInt(0XFF38B6FF),
                          ),
                          pw.Column(
                              // mainAxisAlignment: pw.MainAxisAlignment.start,
                              crossAxisAlignment: pw.CrossAxisAlignment.end,
                              children: [
                                pw.Text(
                                    userInvoiceData.invoiceData.academy.phone
                                        .toString(),
                                    textAlign: pw.TextAlign.end,
                                    style: pw.TextStyle(
                                      color: PdfColors.black,
                                      fontWeight: pw.FontWeight.normal,
                                    )),
                                pw.SizedBox(
                                  height: MySize.getScaledSizeHeight(10),
                                ),
                                pw.SizedBox(
                                  width: MySize.getScaledSizeWidth(300),
                                  child: pw.Text(
                                      textAlign: pw.TextAlign.end,
                                      "GST No :- ${userInvoiceData.invoiceData.academy.gstNo}",
                                      style: pw.TextStyle(
                                          color: PdfColors.black,
                                          font: fontBold)),
                                )
                              ])
                        ]),
                    pw.SizedBox(
                      height: MySize.getScaledSizeHeight(25),
                    ),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Expanded(
                              child: pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  children: [
                                pw.Text(
                                    textAlign: pw.TextAlign.start,
                                    "Student Name- ${userInvoiceData.invoiceData.user.name}",
                                    style: pw.TextStyle(
                                        color: PdfColors.black,
                                        font: fontBold,
                                        fontSize:
                                            MySize.getScaledSizeHeight(14))),
                                pw.SizedBox(
                                    height: MySize.getScaledSizeHeight(5)),
                                pw.Text(
                                    textAlign: pw.TextAlign.end,
                                    "Parents Name- ${userInvoiceData.invoiceData.user.parentName}",
                                    style: pw.TextStyle(
                                        color: PdfColors.black,
                                        font: fontBold,
                                        fontSize:
                                            MySize.getScaledSizeHeight(14))),
                                pw.SizedBox(
                                    height: MySize.getScaledSizeHeight(5)),
                                pw.Text(
                                    textAlign: pw.TextAlign.end,
                                    "Contact No.- ${userInvoiceData.invoiceData.user.mobile}",
                                    style: pw.TextStyle(
                                        color: PdfColors.black,
                                        font: fontBold,
                                        fontSize:
                                            MySize.getScaledSizeHeight(14))),
                                pw.SizedBox(
                                    height: MySize.getScaledSizeHeight(5)),
                                pw.RichText(
                                    text: pw.TextSpan(children: [
                                  pw.TextSpan(
                                      text: "Address- ",
                                      style: pw.TextStyle(
                                          font: fontBold,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(14))),
                                  pw.TextSpan(
                                      text: userInvoiceData
                                          .invoiceData.user.address,
                                      // text: userInvoiceData.invoiceData.user.address,
                                      style: pw.TextStyle(
                                          font: fontRegular,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(14)))
                                ])),
                              ])),
                          pw.SizedBox(width: MySize.getScaledSizeWidth(20)),
                          pw.Expanded(
                              child: pw.Column(
                                  crossAxisAlignment:
                                      pw.CrossAxisAlignment.start,
                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                  children: [
                                pw.RichText(
                                    text: pw.TextSpan(children: [
                                  pw.TextSpan(
                                      text: "Plan Cycle- ",
                                      style: pw.TextStyle(
                                          font: fontBold,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16))),
                                  pw.TextSpan(
                                      text: isForCharge
                                          ? "-"
                                          : userInvoiceData
                                              .invoiceData.user.planCycle,
                                      style: pw.TextStyle(
                                          font: fontRegular,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16)))
                                ])),
                                pw.SizedBox(
                                    height: MySize.getScaledSizeHeight(5)),
                                pw.RichText(
                                    text: pw.TextSpan(children: [
                                  pw.TextSpan(
                                      text: "Invoice ",
                                      style: pw.TextStyle(
                                          font: fontBold,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16))),
                                  pw.TextSpan(
                                      text:
                                          "#${userInvoiceData.invoiceData.user.receipt}",
                                      style: pw.TextStyle(
                                          font: fontRegular,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16)))
                                ])),
                                pw.SizedBox(
                                    height: MySize.getScaledSizeHeight(5)),
                                pw.RichText(
                                    text: pw.TextSpan(children: [
                                  pw.TextSpan(
                                      text: "Date  ",
                                      style: pw.TextStyle(
                                          font: fontBold,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16))),
                                  pw.TextSpan(
                                      text: date,
                                      style: pw.TextStyle(
                                          font: fontRegular,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16)))
                                ])),
                                pw.SizedBox(
                                    height: MySize.getScaledSizeHeight(5)),
                                pw.RichText(
                                    text: pw.TextSpan(children: [
                                  pw.TextSpan(
                                      text: "Transaction  ID ",
                                      style: pw.TextStyle(
                                          font: fontBold,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16))),
                                  pw.TextSpan(
                                      text: userInvoiceData
                                          .invoiceData.user.transactionId,
                                      style: pw.TextStyle(
                                          font: fontRegular,
                                          color: const PdfColor.fromInt(
                                              0xFF262626),
                                          fontSize:
                                              MySize.getScaledSizeHeight(16)))
                                ])),
                              ]))
                        ]),
                    pw.SizedBox(
                      height: MySize.getScaledSizeHeight(10),
                    ),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: []),
                    pw.SizedBox(
                      height: MySize.getScaledSizeHeight(10),
                    ),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: []),
                    pw.SizedBox(
                      height: MySize.getScaledSizeHeight(10),
                    ),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          // pw.SizedBox(
                          //   width: MySize.getScaledSizeWidth(250),
                          //   child: pw.RichText(
                          //       text: pw.TextSpan(children: [
                          //         pw.TextSpan(
                          //             text: "Address -  ",
                          //             style: pw.TextStyle(
                          //                 font: fontBold,
                          //                 color: const PdfColor.fromInt(0xFF262626),
                          //                 fontSize: MySize.getScaledSizeHeight(16))),
                          //         pw.TextSpan(
                          //           text: "asdacvsdafsvarvrwgvfqfrvwqefwdcqrwfvqrwvf  vqerfv",
                          //             // text: userInvoiceData.invoiceData.user.address,
                          //             style: pw.TextStyle(
                          //                 font: fontRegular,
                          //                 color: const PdfColor.fromInt(0xFF262626),
                          //                 fontSize: MySize.getScaledSizeHeight(16)))
                          //       ])),
                          // ),
                          pw.SizedBox(width: MySize.getScaledSizeWidth(10)),
                          // pw.SizedBox(
                          //   width: MySize.getScaledSizeWidth(200),
                          //   child: pw.RichText(
                          //       text: pw.TextSpan(children: [
                          //         pw.TextSpan(
                          //             text: "Transaction  ID ",
                          //             style: pw.TextStyle(
                          //                 font: fontBold,
                          //                 color: const PdfColor.fromInt(0xFF262626),
                          //                 fontSize: MySize.getScaledSizeHeight(16))),
                          //         pw.TextSpan(
                          //             text: userInvoiceData
                          //                 .invoiceData.user.transactionId,
                          //             style: pw.TextStyle(
                          //                 font: fontRegular,
                          //                 color: const PdfColor.fromInt(0xFF262626),
                          //                 fontSize: MySize.getScaledSizeHeight(16)))
                          //       ])),
                          // )
                        ]),
                    pw.SizedBox(height: MySize.getScaledSizeHeight(25)),
                    pw.Table(
                      border: pw.TableBorder.all(),
                      columnWidths: {
                        0: const pw.FlexColumnWidth(),
                        1: const pw.FlexColumnWidth(),
                        2: const pw.FlexColumnWidth(),
                        3: const pw.FlexColumnWidth(),
                        4: const pw.FlexColumnWidth(),
                      },
                      children: [
                        pw.TableRow(
                          decoration: const pw.BoxDecoration(
                            color: PdfColor.fromInt(0xFFECF0F2),
                          ),
                          children: [
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8.0),
                              child: pw.Text(
                                "Item",
                                style: pw.TextStyle(
                                  font: fontRegular,
                                  color: const PdfColor.fromInt(0xFF737373),
                                  fontSize: MySize.getScaledSizeHeight(14),
                                ),
                              ),
                            ),
                            if (isForCharge)
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8.0),
                                child: pw.Text(
                                  "Quantity",
                                  style: pw.TextStyle(
                                    font: fontRegular,
                                    color: const PdfColor.fromInt(0xFF737373),
                                    fontSize: MySize.getScaledSizeHeight(14),
                                  ),
                                ),
                              ),
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8.0),
                              child: pw.Text(
                                "HSN/SAC",
                                style: pw.TextStyle(
                                  font: fontRegular,
                                  color: const PdfColor.fromInt(0xFF737373),
                                  fontSize: MySize.getScaledSizeHeight(14),
                                ),
                              ),
                            ),
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8.0),
                              child: pw.Text(
                                "Price",
                                style: pw.TextStyle(
                                  font: fontRegular,
                                  color: const PdfColor.fromInt(0xFF737373),
                                  fontSize: MySize.getScaledSizeHeight(14),
                                ),
                              ),
                            ),
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8.0),
                              child: pw.Text(
                                "Tax",
                                style: pw.TextStyle(
                                  font: fontRegular,
                                  color: const PdfColor.fromInt(0xFF737373),
                                  fontSize: MySize.getScaledSizeHeight(14),
                                ),
                              ),
                            ),
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8.0),
                              child: pw.Text(
                                "Tax Amount",
                                style: pw.TextStyle(
                                  font: fontRegular,
                                  color: const PdfColor.fromInt(0xFF737373),
                                  fontSize: MySize.getScaledSizeHeight(14),
                                ),
                              ),
                            ),
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8.0),
                              child: pw.Text(
                                "Total",
                                style: pw.TextStyle(
                                  font: fontRegular,
                                  color: const PdfColor.fromInt(0xFF737373),
                                  fontSize: MySize.getScaledSizeHeight(14),
                                ),
                              ),
                            ),
                          ],
                        ),
                        ...items.asMap().entries.map((entry) {
                          return pw.TableRow(
                            children: [
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8.0),
                                child: pw.Text(
                                  entry.value.item,
                                  style: pw.TextStyle(
                                    font: fontSemiBold,
                                    fontSize: MySize.getScaledSizeHeight(12),
                                  ),
                                ),
                              ),
                              if (isForCharge)
                                pw.Padding(
                                  padding: const pw.EdgeInsets.all(8.0),
                                  child: pw.Text(
                                    entry.value.quantity.toString(),
                                    style: pw.TextStyle(
                                      font: fontSemiBold,
                                      fontSize: MySize.getScaledSizeHeight(12),
                                    ),
                                  ),
                                ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8.0),
                                child: pw.Text(
                                  entry.value.hncSac.toString(),
                                  style: pw.TextStyle(
                                    font: fontSemiBold,
                                    fontSize: MySize.getScaledSizeHeight(12),
                                  ),
                                ),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8.0),
                                child: pw.Text(
                                  entry.value.price.toString(),
                                  style: pw.TextStyle(
                                    font: fontSemiBold,
                                    fontSize: MySize.getScaledSizeHeight(12),
                                  ),
                                ),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8.0),
                                child: pw.Text(
                                  "${"CGST (${entry.value.tax.cgst} %)"}\n${"SGST (${entry.value.tax.sgst} %)"}",
                                  style: pw.TextStyle(
                                    font: fontSemiBold,
                                    fontSize: MySize.getScaledSizeHeight(12),
                                  ),
                                ),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8.0),
                                child: pw.Text(
                                  "${(entry.value.taxAmount! / 2).toStringAsFixed(2)}\n${(entry.value.taxAmount! / 2).toStringAsFixed(2)}",
                                  // "${((entry.value.price) * (entry.value.tax.cgst / 100)) / (entry.value.quantity?.round() ?? 1)}\n${((entry.value.price) * (entry.value.tax.sgst / 100)) / (entry.value.quantity?.round() ?? 1)}",
                                  style: pw.TextStyle(
                                    font: fontSemiBold,
                                    fontSize: MySize.getScaledSizeHeight(12),
                                  ),
                                ),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8.0),
                                child: pw.Text(
                                  entry.value.amount.toString(),
                                  style: pw.TextStyle(
                                    font: fontSemiBold,
                                    fontSize: MySize.getScaledSizeHeight(12),
                                  ),
                                ),
                              ),
                            ],
                          );
                        }),
                      ],
                    ),
                    pw.SizedBox(height: MySize.getScaledSizeHeight(15)),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Text("Total",
                              style: pw.TextStyle(
                                  font: fontBold,
                                  fontSize: MySize.getScaledSizeHeight(20))),
                          pw.SizedBox(width: MySize.getScaledSizeWidth(15)),
                          pw.Column(children: [
                            pw.Text("INR",
                                style: pw.TextStyle(
                                    font: fontSemiBold,
                                    fontSize: MySize.getScaledSizeHeight(20))),
                            pw.Text(
                                userInvoiceData.invoiceData.subtotal.toString(),
                                style: pw.TextStyle(
                                    font: fontBold,
                                    fontSize: MySize.getScaledSizeHeight(25))),
                            pw.Container(
                                color: PdfColors.black,
                                height: MySize.getScaledSizeHeight(2),
                                width: MySize.getScaledSizeWidth(70))
                          ]),
                        ]),
                    pw.SizedBox(height: MySize.getScaledSizeHeight(25)),
                    pw.Row(
                        mainAxisAlignment: netImage2 != null
                            ? pw.MainAxisAlignment.spaceBetween
                            : pw.MainAxisAlignment.end,
                        children: [
                          if (netImage2 != null)
                            pw.Image(netImage2,
                                width: MySize.getScaledSizeWidth(200),
                                height: MySize.getScaledSizeHeight(150)),
                          pw.RichText(
                              text: pw.TextSpan(children: [
                            pw.TextSpan(
                                text: "PAYMENT STATUS- ",
                                style: pw.TextStyle(
                                    font: fontBold,
                                    color: const PdfColor.fromInt(0xFF262626),
                                    fontSize: MySize.getScaledSizeHeight(16))),
                            pw.TextSpan(
                                text: userInvoiceData.invoiceData.paid == 0
                                    ? "PAID"
                                    : "UNPAID",
                                style: pw.TextStyle(
                                    font: fontBold,
                                    color: PdfColor.fromInt(
                                        userInvoiceData.invoiceData.paid == 0
                                            ? 0xff108B24
                                            : 0xFFFF3131),
                                    fontSize: MySize.getScaledSizeHeight(16)))
                          ])),
                        ]),
                    pw.SizedBox(height: MySize.getScaledSizeHeight(10)),
                    if (netImage2 != null)
                      pw.Text(userInvoiceData.invoiceData.academy.name,
                          style: pw.TextStyle(
                              fontSize: MySize.getScaledSizeHeight(12),
                              font: fontBold)),
                    if (netImage2 != null)
                      pw.SizedBox(height: MySize.getScaledSizeHeight(10)),
                    if (netImage2 != null)
                      pw.Text("Authorised Signatory",
                          style: pw.TextStyle(
                              fontSize: MySize.getScaledSizeHeight(12),
                              font: fontSemiBold)),
                  ]),
            )));

    try {
      // DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      // AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;

      await requestPermissions(
        context,
        permissions: [
          Platform.isIOS ? Permission.photos : Permission.mediaLibrary
        ],
        onPermissionsGranted: () async {
          // Directory? tempDir = Platform.isIOS
          //     ? await getApplicationSupportDirectory()
          //     : await getApplicationSupportDirectory();
          // // final tempDir = Directory('/storage/emulated/0/Download/khelnet');
          // // if (!(await tempDir.exists())) {
          // //   await tempDir.create(recursive: true);
          // // }
          final path = await getDownloadFolderPath();
          final file = File(
              '$path/Report-#${userInvoiceData.invoiceData.user.receipt}.pdf');
          try {
            await file.writeAsBytes(await pdf.save()).then((value) async {
              if (file.path.isNotEmpty && context.mounted && file.path != "") {
                CustomDialog.hideLoader(context);
                if (!isSharing) {
                  context.pushNamed(RouteConstant.pdfViewScreen,
                      args: {"pdfPath": file.path});
                } else {
                  final pdfFile = File(file.path);
                  final pdfFileName = file.path.split('/').last;

                  final bytes = await pdfFile.readAsBytes();

                  final xFile = XFile.fromData(bytes,
                      name: pdfFileName, mimeType: 'application/pdf');

                  await Share.shareXFiles([xFile], text: 'Sharing PDF');
                }
              }
            });
          } catch (e) {
            CustomDialog.hideLoader(context);
            ToastUtils.showFailed(message: "Something went wrong");
            log("Error generating PDF: $e");
          }
          log('PDF saved at ${file.path}');
        },
      );
    } catch (e) {
      CustomDialog.hideLoader(context);
      ToastUtils.showFailed(message: "Something went wrong");
      log("Error generating PDF: $e");
    }
  }

  // static askPermission() async {
  //   DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  //   AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;
  //   if (androidDeviceInfo.version.sdkInt < 28) {
  //     await Permission.storage.isDenied.then((value) {
  //       if (value) {
  //         Permission.storage.request();
  //       }
  //     });
  //   } else {
  //     await Permission.manageExternalStorage.isDenied.then((value) {
  //       if (value) {
  //         Permission.manageExternalStorage.request();
  //       }
  //     });
  //   }
  // }

  static getTheLocation(BuildContext context,
      {required Permission permission,
      required List<Centers> centers,
      required bool isForClockIn,
      required Centers initialSelectedCenter,
      required void Function(
              double lat, double long, String address, int centerId)
          onLocationFetched}) async {
    PermissionStatus permissionStatus = await permission.status;
    log('permission status ===> $permissionStatus');
    if (permissionStatus.isGranted) {
      if (await Geolocator.isLocationServiceEnabled() && context.mounted) {
        if (isForClockIn && context.mounted) {
          if (centers.length > 1) {
            SingleSelectionBottomSheet.selectCenter(
              isForLocation: true,
              context,
              onTap: (Centers? selectedCenter) async {
                context.pop();
                SingleSelectionBottomSheet.locationFetching(context);
                Position position = await Geolocator.getCurrentPosition(
                    desiredAccuracy: LocationAccuracy.high);
                List<Placemark> placeMarks = await placemarkFromCoordinates(
                    position.latitude, position.longitude);

                Placemark place = placeMarks[0];
                log("${place.subLocality!}, ${place.administrativeArea!}, ${place.country!}, ${position.latitude.toString()}, ${position.longitude.toString()}");
                onLocationFetched(
                    position.latitude,
                    position.longitude,
                    "${place.subLocality}, ${place.locality}, ${place.administrativeArea}-${place.postalCode}",
                    selectedCenter!.id);
                initialSelectedCenter = selectedCenter;
              },
              centers: centers,
              initialSelectedCenter: initialSelectedCenter,
            );
          } else {
            SingleSelectionBottomSheet.locationFetching(context);
            Position position = await Geolocator.getCurrentPosition(
                desiredAccuracy: LocationAccuracy.high);
            List<Placemark> placeMarks = await placemarkFromCoordinates(
                position.latitude, position.longitude);

            Placemark place = placeMarks[0];
            log("${place.subLocality!}, ${place.administrativeArea!}, ${place.country!}, ${position.latitude.toString()}, ${position.longitude.toString()}");
            onLocationFetched(
                position.latitude,
                position.longitude,
                "${place.subLocality}, ${place.locality}, ${place.administrativeArea}-${place.postalCode}",
                centers[0].id);
          }
        } else {
          SingleSelectionBottomSheet.locationFetching(context);
          Position position = await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.high);
          List<Placemark> placeMarks = await placemarkFromCoordinates(
              position.latitude, position.longitude);

          Placemark place = placeMarks[0];

          onLocationFetched(
              position.latitude,
              position.longitude,
              "${place.subLocality}, ${place.locality}, ${place.administrativeArea}-${place.postalCode}",
              0);
        }
      } else {
        showDialog(
          context: context,
          builder: (context) {
            return PopScope(
                canPop: true,
                child: Align(
                  child: Material(
                    color: ColorConstant.white,
                    elevation: 10,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: SizedBox(
                      height: MySize.getScaledSizeHeight(200),
                      width: MySize.getScaledSizeWidth(270),
                      child: Padding(
                        padding: const EdgeInsets.all(18.0),
                        child: Column(
                          children: [
                            Align(
                                alignment: Alignment.topRight,
                                child: GestureDetector(
                                  onTap: () => context.pop(),
                                  child: const CircleAvatar(
                                      backgroundColor: ColorConstant.skyBlue,
                                      radius: 10,
                                      child:
                                          CustomSvgPicture(AppAsset.crossIcon)),
                                )),
                            Gap(MySize.getScaledSizeHeight(10)),
                            Expanded(
                              child: GradientText(
                                gradient: const LinearGradient(colors: [
                                  ColorConstant.white,
                                  ColorConstant.primaryColor
                                ]),
                                child: Text(
                                  "Turn On Location Services to Allow Maps to Determine Your Location",
                                  style: GoogleFonts.poppins(
                                      textStyle: TextStyle(
                                          color: ColorConstant.primaryColor,
                                          fontSize:
                                              MySize.getScaledSizeHeight(15),
                                          fontWeight: FontWeight.w600)),
                                ),
                              ),
                            ),
                            Align(
                              alignment: Alignment.bottomRight,
                              child: GestureDetector(
                                onTap: () {
                                  AppSettings.openAppSettings(
                                          type: AppSettingsType.location)
                                      .then(
                                    (value) {
                                      context.pop();
                                    },
                                  );
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 8.0),
                                  child: GradientText(
                                    gradient: const LinearGradient(colors: [
                                      ColorConstant.white,
                                      ColorConstant.primaryColor
                                    ]),
                                    child: TypoGraphy.text("OK",
                                        color: ColorConstant.primaryColor,
                                        level: 3),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ));
          },
        );
      }
    } else if (permissionStatus.isDenied) {
      PermissionStatus permissionStatus = await permission.request();
      log('permission status in denied stion===> $permissionStatus');
      if (permissionStatus.isGranted && context.mounted) {
        Position position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high);
        log(position.latitude.toString());
        log(position.longitude.toString());
        if (await Geolocator.isLocationServiceEnabled() && context.mounted) {
          if (isForClockIn && context.mounted) {
            if (centers.length > 1) {
              SingleSelectionBottomSheet.selectCenter(
                isForLocation: true,
                context,
                onTap: (Centers? selectedCenter) async {
                  context.pop();
                  SingleSelectionBottomSheet.locationFetching(context);
                  Position position = await Geolocator.getCurrentPosition(
                      desiredAccuracy: LocationAccuracy.high);
                  List<Placemark> placeMarks = await placemarkFromCoordinates(
                      position.latitude, position.longitude);

                  Placemark place = placeMarks[0];
                  log("${place.subLocality!}, ${place.administrativeArea!}, ${place.country!}, ${position.latitude.toString()}, ${position.longitude.toString()}");
                  onLocationFetched(
                      position.latitude,
                      position.longitude,
                      "${place.subLocality}, ${place.locality}, ${place.administrativeArea}-${place.postalCode}",
                      selectedCenter!.id);
                  initialSelectedCenter = selectedCenter;
                },
                centers: centers,
                initialSelectedCenter: initialSelectedCenter,
              );
            } else {
              SingleSelectionBottomSheet.locationFetching(context);
              Position position = await Geolocator.getCurrentPosition(
                  desiredAccuracy: LocationAccuracy.high);
              List<Placemark> placeMarks = await placemarkFromCoordinates(
                  position.latitude, position.longitude);

              Placemark place = placeMarks[0];
              log("${place.subLocality!}, ${place.administrativeArea!}, ${place.country!}, ${position.latitude.toString()}, ${position.longitude.toString()}");
              onLocationFetched(
                  position.latitude,
                  position.longitude,
                  "${place.subLocality}, ${place.locality}, ${place.administrativeArea}-${place.postalCode}",
                  centers[0].id);
            }
          } else {
            SingleSelectionBottomSheet.locationFetching(context);
            Position position = await Geolocator.getCurrentPosition(
                desiredAccuracy: LocationAccuracy.high);
            List<Placemark> placeMarks = await placemarkFromCoordinates(
                position.latitude, position.longitude);

            Placemark place = placeMarks[0];
            log("${place.subLocality!}, ${place.administrativeArea!}, ${place.country!}, ${position.latitude.toString()}, ${position.longitude.toString()}");

            onLocationFetched(
                position.latitude,
                position.longitude,
                "${place.subLocality}, ${place.locality}, ${place.administrativeArea}-${place.postalCode}",
                0);
          }
        } else {
          showDialog(
            context: context,
            builder: (context) {
              return PopScope(
                  canPop: true,
                  child: Align(
                    child: Material(
                      color: ColorConstant.white,
                      elevation: 10,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: SizedBox(
                        height: MySize.getScaledSizeHeight(200),
                        width: MySize.getScaledSizeWidth(270),
                        child: Padding(
                          padding: const EdgeInsets.all(18.0),
                          child: Column(
                            children: [
                              Align(
                                  alignment: Alignment.topRight,
                                  child: GestureDetector(
                                    onTap: () => context.pop(),
                                    child: const CircleAvatar(
                                        backgroundColor: ColorConstant.skyBlue,
                                        radius: 10,
                                        child: CustomSvgPicture(
                                            AppAsset.crossIcon)),
                                  )),
                              Gap(MySize.getScaledSizeHeight(10)),
                              Expanded(
                                child: GradientText(
                                  gradient: const LinearGradient(colors: [
                                    ColorConstant.white,
                                    ColorConstant.primaryColor
                                  ]),
                                  child: Text(
                                    "Turn On Location Services to Allow Maps to Determine Your Location",
                                    style: GoogleFonts.poppins(
                                        textStyle: TextStyle(
                                            color: ColorConstant.primaryColor,
                                            fontSize:
                                                MySize.getScaledSizeHeight(15),
                                            fontWeight: FontWeight.w600)),
                                  ),
                                ),
                              ),
                              Align(
                                alignment: Alignment.bottomRight,
                                child: GestureDetector(
                                  onTap: () {
                                    AppSettings.openAppSettings(
                                            type: AppSettingsType.location)
                                        .then(
                                      (value) {
                                        context.pop();
                                      },
                                    );
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: GradientText(
                                      gradient: const LinearGradient(colors: [
                                        ColorConstant.white,
                                        ColorConstant.primaryColor
                                      ]),
                                      child: TypoGraphy.text("OK",
                                          color: ColorConstant.primaryColor,
                                          level: 3),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ));
            },
          );
        }
      }
    } else if (permissionStatus.isPermanentlyDenied && context.mounted) {
      showDialog(
        context: context,
        builder: (context) {
          return PopScope(
              canPop: true,
              child: Align(
                child: Material(
                  color: ColorConstant.white,
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: SizedBox(
                    height: MySize.getScaledSizeHeight(200),
                    width: MySize.getScaledSizeWidth(320),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(18),
                          vertical: MySize.getScaledSizeHeight(18)),
                      child: Column(
                        children: [
                          Align(
                              alignment: Alignment.topRight,
                              child: GestureDetector(
                                onTap: () => context.pop(),
                                child: const CircleAvatar(
                                    backgroundColor: ColorConstant.skyBlue,
                                    radius: 10,
                                    child:
                                        CustomSvgPicture(AppAsset.crossIcon)),
                              )),
                          Gap(MySize.getScaledSizeHeight(10)),
                          Expanded(
                            child: Text(
                              "Enable your location permission for clock in",
                              style: GoogleFonts.poppins(
                                  textStyle: TextStyle(
                                      color: ColorConstant.black,
                                      fontSize: MySize.getScaledSizeHeight(15),
                                      fontWeight: FontWeight.w600)),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.getScaledSizeWidth(8)),
                            child: Align(
                              alignment: Alignment.centerRight,
                              child: GestureDetector(
                                onTap: () async {
                                  await openAppSettings();
                                },
                                child: GradientText(
                                  gradient: const LinearGradient(colors: [
                                    ColorConstant.white,
                                    ColorConstant.primaryColor,
                                  ]),
                                  child: TypoGraphy.text(
                                      textAlign: TextAlign.start,
                                      "Enable location permission",
                                      color: ColorConstant.primaryColor,
                                      fontSize: MySize.getScaledSizeHeight(16),
                                      fontWeight: FontWeight.w700),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ));
        },
      );
    }
  }

  static Future<void> requestPermissions(
    BuildContext context, {
    required List<Permission> permissions,
    required VoidCallback onPermissionsGranted,
    String? message,
    String? title,
  }) async {
    // Request permissions
    Map<Permission, PermissionStatus> statuses = await permissions.request();

    // Check if all permissions are granted
    bool allGranted = statuses.values.every((status) => status.isGranted || status.isLimited);

    if (allGranted) {
      onPermissionsGranted();
    } else {
      // Check if any permission is permanently denied
      bool permanentlyDenied =
          statuses.values.any((status) => status.isPermanentlyDenied);

      if (permanentlyDenied) {
        showDialog(
          context: context,
          builder: (context) {
            return PopScope(
                canPop: true,
                child: Align(
                  child: Material(
                    color: ColorConstant.white,
                    elevation: 10,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: SizedBox(
                      height: MySize.getScaledSizeHeight(200),
                      width: MySize.getScaledSizeWidth(270),
                      child: Padding(
                        padding: const EdgeInsets.all(18.0),
                        child: Column(
                          children: [
                            Align(
                                alignment: Alignment.topRight,
                                child: GestureDetector(
                                  onTap: () => context.pop(),
                                  child: const CircleAvatar(
                                      backgroundColor: ColorConstant.skyBlue,
                                      radius: 10,
                                      child:
                                          CustomSvgPicture(AppAsset.crossIcon)),
                                )),
                            Gap(MySize.getScaledSizeHeight(10)),
                            Expanded(
                              child: GradientText(
                                gradient: const LinearGradient(colors: [
                                  ColorConstant.white,
                                  ColorConstant.primaryColor
                                ]),
                                child: Text(
                                  "This permission is required",
                                  style: GoogleFonts.poppins(
                                      textStyle: TextStyle(
                                          color: ColorConstant.primaryColor,
                                          fontSize:
                                              MySize.getScaledSizeHeight(15),
                                          fontWeight: FontWeight.w600)),
                                ),
                              ),
                            ),
                            Align(
                              alignment: Alignment.bottomRight,
                              child: GestureDetector(
                                onTap: () {
                                  AppSettings.openAppSettings(
                                          type: AppSettingsType.location)
                                      .then(
                                    (value) {
                                      context.pop();
                                    },
                                  );
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 8.0),
                                  child: GradientText(
                                    gradient: const LinearGradient(colors: [
                                      ColorConstant.white,
                                      ColorConstant.primaryColor
                                    ]),
                                    child: TypoGraphy.text("OK",
                                        color: ColorConstant.primaryColor,
                                        level: 3),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ));
          },
        );
      } else {
        await requestPermissions(
          context,
          permissions: permissions,
          onPermissionsGranted: onPermissionsGranted,
        );
      }
    }
  }

  Future initializeNotification() async {
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    AndroidInitializationSettings initializationSettingsAndroid =
        const AndroidInitializationSettings("@mipmap/ic_launcher");

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: true,
      requestAlertPermission: true,
    );

    final InitializationSettings initializationSettings =
        InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS);
    await flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: selectNotification);
  }

  Future selectNotification(NotificationResponse? notificationResponse) async {
    log(notificationResponse!.payload.toString());

    if (notificationResponse.payload!.contains("pdf")) {
      await OpenFilex.open(notificationResponse.payload!,
          type: "application/pdf");
    } else if (notificationResponse.payload!.contains("xlsx")) {
      await OpenFilex.open(
        notificationResponse.payload!,
      );
    } else {
      await OpenFilex.open(
        notificationResponse.payload!,
      );
    }
  }

  showNotification(String filePath) async {
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    AndroidNotificationDetails androidPlatformChannelSpecific =
        const AndroidNotificationDetails('download_pdf', 'download_pdf',
            importance: Importance.max,
            priority: Priority.high,
            icon: "@drawable/ic_launcher",
            ticker: 'ticker');

    const DarwinNotificationDetails iosplatformChannelSpecific =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final NotificationDetails notificationDetails = NotificationDetails(
        android: androidPlatformChannelSpecific,
        iOS: iosplatformChannelSpecific);
    await flutterLocalNotificationsPlugin.show(
        0, 'Download', 'Download Successful', notificationDetails,
        payload: filePath);
  }

  static getFcmToken() async {
    await FirebaseMessaging.instance.getToken().then((value) async {
      log("FCM token is => $value");
      await StorageManager.instance
          .saveData(AppConstant.storageConstant.fcmToken, value!);
      await FirebaseMessaging.instance.subscribeToTopic(CONFIG().env);
      NotificationService().init();
    });
  }

  notificationRoute(String? route) {
    String finalRoute = route!;
    int id = 0;
    if (route.startsWith("ENQUIRY")) {
      finalRoute = route.split("_").first;
      id = int.parse(route.split("_").last);
    }
    switch (finalRoute) {
      case "STUDENT_ATTENDANCE":
        return NavigationManager.navigatorKey.currentContext
            ?.pushNamed(RouteConstant.attendanceScreen);
      case "STUDENT_FEE_TRANSACTION":
        return NavigationManager.navigatorKey.currentContext
            ?.pushNamed(RouteConstant.feesScreen);
      case "ALL_COACH":
        return NavigationManager.navigatorKey.currentContext
            ?.pushNamed(RouteConstant.allCoachScreen);
      case "STUDENT_REMINDER":
        return NavigationManager.navigatorKey.currentContext
            ?.pushNamed(RouteConstant.feesReminderScreen);
      case "PROFILE":
        return NavigationManager.navigatorKey.currentContext
            ?.pushNamed(RouteConstant.homeScreen);
      case "ASSESSMENT_DUE":
        return NavigationManager.navigatorKey.currentContext?.pushNamed(
            RouteConstant.templateStudentScreen,
            args: {"isPending": true});
      case "ENQUIRY":
        return NavigationManager.navigatorKey.currentContext
            ?.pushNamed(RouteConstant.enquiryDetailScreen, args: {"id": id});
      default:
      // return NavigationManager.navigatorKey.currentContext
      //     ?.pushNamed(RouteConstant.homeScreen);
    }
  }

  static String reformatDate(String inputDate) {
    DateFormat inputFormat = DateFormat("yyyy-MM-dd");
    DateFormat outputFormat = DateFormat("dd-MM-yyyy");

    DateTime dateTime = inputFormat.parse(inputDate);
    return outputFormat.format(dateTime);
  }

  static String formatDate(String inputDate) {
    DateFormat inputFormat = DateFormat("dd-MM-yyyy");
    DateFormat outputFormat = DateFormat("yyyy-MM-dd");

    DateTime dateTime = inputFormat.parse(inputDate);
    return outputFormat.format(dateTime);
  }

  static Future<String> getDownloadFolderPath() async {
    if (Platform.isAndroid) {
      final directories = await getExternalStorageDirectories();
      if (directories != null && directories.isNotEmpty) {
        return directories.first.path;
      }
    } else if (Platform.isIOS) {
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    }
    return 'Downloads folder not found!';
  }
}
