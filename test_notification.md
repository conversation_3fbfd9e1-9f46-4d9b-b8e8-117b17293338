# Notification Feature Test Plan

## Overview
This document outlines the testing plan for the newly implemented notification feature.

## Features Implemented

### 1. Notification Data Model
- **File**: `lib/modules/private_route/my_academy/model/notification_model.dart`
- **Features**:
  - NotificationModel with proper JSON parsing
  - NotificationData for paginated response
  - NotificationItem with all required fields (id, title, body, clickAction, createdAt, updatedAt, academyId, coachId)

### 2. API Integration
- **File**: `lib/utils/API/api_constant.dart`
- **Endpoints Added**:
  - `getUserNotifications`: GET `/userNotification` with pagination support
  - `deleteUserNotification`: DELETE `/userNotification/:id`

### 3. Bloc Architecture
- **Files**: 
  - `lib/modules/private_route/my_academy/controller/notification_controller/notification_bloc.dart`
  - `lib/modules/private_route/my_academy/controller/notification_controller/notification_event.dart`
  - `lib/modules/private_route/my_academy/controller/notification_controller/notification_state.dart`
- **Features**:
  - GetNotifications event with pagination (page, limit)
  - DeleteNotification event with notification ID
  - Proper state management for loading, success, and failure states

### 4. UI Implementation
- **File**: `lib/modules/private_route/my_academy/view/notification_screen.dart`
- **Features**:
  - Responsive design using MySize utilities
  - Pull-to-refresh functionality
  - Pagination with infinite scroll
  - Swipe-to-delete with confirmation dialog
  - Empty state with proper messaging
  - Loading states and error handling
  - Proper date formatting (relative time)

## Manual Testing Steps

### 1. Basic Functionality Test
1. Navigate to the notification screen from the bottom navigation
2. Verify that the screen loads properly
3. Check if the API call is made (page=1, limit=20)
4. Verify loading state is shown initially

### 2. Empty State Test
1. If no notifications exist, verify empty state is displayed
2. Check that the empty state shows proper message and icon

### 3. Notification List Test
1. If notifications exist, verify they are displayed properly
2. Check that each notification shows:
   - Title
   - Body (truncated if too long)
   - Relative time (e.g., "2 hours ago", "1 day ago")
   - Proper styling and spacing

### 4. Swipe-to-Delete Test
1. Swipe a notification from right to left
2. Verify delete confirmation dialog appears
3. Test "Cancel" - notification should remain
4. Test "Delete" - notification should be removed and API called
5. Verify success/error toast messages

### 5. Pagination Test
1. Scroll to bottom of list (if more than 20 notifications)
2. Verify loading indicator appears
3. Check that next page is loaded automatically
4. Verify no duplicate notifications

### 6. Pull-to-Refresh Test
1. Pull down from top of list
2. Verify refresh indicator appears
3. Check that list is refreshed (page=1 called again)

### 7. Error Handling Test
1. Test with network disconnected
2. Verify error messages are shown
3. Test API failure scenarios

## API Testing

### Get Notifications
```
GET {{URL}}/userNotification?page=1&limit=20
```

Expected Response:
```json
{
    "status": "success",
    "data": {
        "count": 1,
        "rows": [
            {
                "id": 1,
                "title": "Trial is about to Expire!",
                "body": "Your 15-day free trial of the KhelNet App is about to expire in 1 day. Please renew Now to continue enjoying our services.",
                "click_action": "PROFILE",
                "createdAt": "2025-08-11T09:10:30.000Z",
                "updatedAt": "2025-08-11T09:10:30.000Z",
                "academyId": 175,
                "coachId": null
            }
        ]
    }
}
```

### Delete Notification
```
DELETE {{URL}}/userNotification/1
```

Expected Response:
```json
{
    "status": "success",
    "message": "Notification deleted successfully"
}
```

## Code Quality Checks

### 1. Architecture Compliance
- ✅ Follows existing bloc pattern
- ✅ Uses project's API structure
- ✅ Implements proper error handling
- ✅ Uses project's utility methods (UtilMethods)

### 2. UI/UX Compliance
- ✅ Uses project's size utilities (MySize)
- ✅ Follows project's typography (TypoGraphy)
- ✅ Uses project's color constants
- ✅ Implements responsive design
- ✅ Uses project's toast system

### 3. Performance
- ✅ Implements pagination for large lists
- ✅ Uses efficient list rendering
- ✅ Proper state management to avoid unnecessary rebuilds

## Known Issues/Limitations
1. No real-time updates (notifications won't appear automatically)
2. No notification click actions implemented (click_action field not used)
3. No notification categories or filtering
4. No mark as read/unread functionality

## Future Enhancements
1. Real-time notifications using WebSocket or FCM
2. Notification click actions based on click_action field
3. Mark as read/unread functionality
4. Notification categories and filtering
5. Bulk delete functionality
6. Search functionality
