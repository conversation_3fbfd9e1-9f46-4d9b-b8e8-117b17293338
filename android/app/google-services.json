{"project_info": {"project_number": "474107370641", "project_id": "khelnet-tst", "storage_bucket": "khelnet-tst.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:474107370641:android:d5263da7118246ab2bb330", "android_client_info": {"package_name": "com.khelnet"}}, "oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDDoxiGuyAJGEKo_-CdLM8MokmKrNCIQxA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}, {"client_id": "474107370641-toc4dfr2nd80oeslnvgk78jli0q5odlp.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.khelnet.user"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:474107370641:android:5fd10bc882672b8d2bb330", "android_client_info": {"package_name": "com.khelnet.app"}}, "oauth_client": [{"client_id": "474107370641-4hv6dmc21p9h931dh8ke0ut92gupeflk.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.khelnet.app", "certificate_hash": "3353a7e2a6ad5291defe0e1399c6dfe827b5c9ad"}}, {"client_id": "474107370641-75ei6f7lrdo11e7ldflk0m3lvct24nd2.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.khelnet.app", "certificate_hash": "49402ed338d9129a45ced810e348625ff7125e52"}}, {"client_id": "474107370641-7choj9chkpl1tct715jb3rdl1j1kuo84.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.khelnet.app", "certificate_hash": "fff2085312503ef012eabe0a691a93ff8820ad27"}}, {"client_id": "474107370641-ds8ec0u3mbbfvjcvb2k4mohdjo91bdd1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.khelnet.app", "certificate_hash": "aa43adacfdbd2b483b8acdf079878adc5b2da345"}}, {"client_id": "474107370641-hlaem1sg05ap4m3opua8rl4pjaaigffv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.khelnet.app", "certificate_hash": "18a21be6175f8c3f926e732a6d9cb95f0cd90e4e"}}, {"client_id": "474107370641-snra7na4p6ge91cgs8q1p3hv4knhane4.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.khelnet.app", "certificate_hash": "4caaba98b645f857028fcdb647b693528f6e1f30"}}, {"client_id": "474107370641-up5me4ro53j3nmvpcdifnj38add7pubp.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.khelnet.app", "certificate_hash": "022cd68b9557ee69a7caedc43525b9dca62848ec"}}, {"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDDoxiGuyAJGEKo_-CdLM8MokmKrNCIQxA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}, {"client_id": "474107370641-toc4dfr2nd80oeslnvgk78jli0q5odlp.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.khelnet.user"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:474107370641:android:baaf132c6e35e8642bb330", "android_client_info": {"package_name": "com.khelnet.chennaiyin"}}, "oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDDoxiGuyAJGEKo_-CdLM8MokmKrNCIQxA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}, {"client_id": "474107370641-toc4dfr2nd80oeslnvgk78jli0q5odlp.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.khelnet.user"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:474107370641:android:aba28dbe601ca6cb2bb330", "android_client_info": {"package_name": "com.khelnet.user"}}, "oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDDoxiGuyAJGEKo_-CdLM8MokmKrNCIQxA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}, {"client_id": "474107370641-toc4dfr2nd80oeslnvgk78jli0q5odlp.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.khelnet.user"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:474107370641:android:9011726f9b1b17882bb330", "android_client_info": {"package_name": "in.khelnet.chennaiyin"}}, "oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDDoxiGuyAJGEKo_-CdLM8MokmKrNCIQxA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "474107370641-qs3p08ufht5fi150v7mabuo3372kqd5m.apps.googleusercontent.com", "client_type": 3}, {"client_id": "474107370641-toc4dfr2nd80oeslnvgk78jli0q5odlp.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.khelnet.user"}}]}}}], "configuration_version": "1"}